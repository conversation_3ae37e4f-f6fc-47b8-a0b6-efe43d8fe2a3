"""
Thumbnail generation and management functionality.
"""

import hashlib
import logging
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from PIL import Image

from .cache import cache_result
from .database import initialize_database, store_thumbnail_info
from .models import PhotoInfo, ProgressCallback, ThumbnailConfig, ThumbnailInfo

logger = logging.getLogger(__name__)

# 定义并发处理的缩略图数量
MAX_WORKERS = 4


@cache_result(ttl=3600)  # 缓存1小时
def generate_single_thumbnail(photo: PhotoInfo, config: ThumbnailConfig) -> Optional[ThumbnailInfo]:
    """
    Generate a thumbnail for a single photo.

    Args:
        photo: Photo to generate thumbnail for
        config: Thumbnail generation configuration

    Returns:
        ThumbnailInfo object or None if generation failed
    """
    try:
        if not os.path.exists(photo.original_path):
            logger.warning(f"Photo file not found: {photo.original_path}")
            return None

        # Generate thumbnail filename
        thumbnail_filename = generate_thumbnail_filename(photo)
        thumbnail_path = os.path.join(config.output_dir, thumbnail_filename)

        # Skip if thumbnail already exists and is newer than source
        if os.path.exists(thumbnail_path):
            source_mtime = os.path.getmtime(photo.original_path)
            thumb_mtime = os.path.getmtime(thumbnail_path)
            if thumb_mtime >= source_mtime:
                logger.debug(f"Thumbnail already exists and is current: {thumbnail_path}")
                return create_thumbnail_info(photo, thumbnail_path, config)

        # Generate the thumbnail
        with Image.open(photo.original_path) as img:
            # Convert to RGB if necessary
            if img.mode in ("RGBA", "LA", "P"):
                background = Image.new("RGB", img.size, (255, 255, 255))
                if img.mode == "P":
                    img = img.convert("RGBA")
                background.paste(img, mask=img.split()[-1] if img.mode in ("RGBA", "LA") else None)
                img = background
            elif img.mode != "RGB":
                img = img.convert("RGB")

            # Calculate thumbnail size maintaining aspect ratio
            max_width, max_height = config.max_width or config.dimensions[0], config.max_height or config.dimensions[1]
            img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)

            # Save thumbnail
            save_kwargs = {"format": config.format, "quality": config.quality, "optimize": True}

            if config.format.upper() == "JPEG":
                save_kwargs["progressive"] = True

            img.save(thumbnail_path, **save_kwargs)

        return create_thumbnail_info(photo, thumbnail_path, config)

    except Exception as e:
        logger.error(f"Error generating thumbnail for {photo.filename}: {e}")
        return None


def process_thumbnail_batch(photo_data):
    """处理单个缩略图的函数，用于并发处理"""
    photo, config, progress_callback, i, total = photo_data
    if progress_callback:
        progress_callback("thumbnails", i + 1, total, f"Generating thumbnail for {photo.filename}")

    try:
        thumbnail = generate_single_thumbnail(photo, config)
        return thumbnail
    except Exception as e:
        logger.warning(f"Failed to generate thumbnail for {photo.filename}: {e}")
        return None


def generate_thumbnails_batch(photos: List[PhotoInfo], config: ThumbnailConfig, progress_callback: Optional[ProgressCallback] = None, db_path: str = None) -> List[ThumbnailInfo]:
    """
    Generate thumbnails for a batch of photos.

    Args:
        photos: List of photos to generate thumbnails for
        config: Thumbnail generation configuration
        progress_callback: Optional callback for progress reporting

    Returns:
        List of ThumbnailInfo objects
    """
    thumbnails = []
    total = len(photos)

    # Ensure output directory exists
    os.makedirs(config.output_dir, exist_ok=True)

    # Initialize database if path provided
    db_session = None
    if db_path:
        try:
            Session = initialize_database(db_path)
            db_session = Session()
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")

    # 使用线程池并发处理缩略图生成
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 准备任务数据
        tasks = [(photo, config, progress_callback, i, total) for i, photo in enumerate(photos)]

        # 提交所有任务
        future_to_index = {executor.submit(process_thumbnail_batch, task_data): i for i, task_data in enumerate(tasks)}

        # 收集结果
        for future in as_completed(future_to_index):
            thumbnail = future.result()
            if thumbnail:
                thumbnails.append(thumbnail)

                # Store in database if session is available
                if db_session:
                    try:
                        store_thumbnail_info(db_session, thumbnail.photo_uuid, thumbnail.to_dict())
                    except Exception as e:
                        logger.warning(f"Failed to store thumbnail in database: {e}")

    # Close database session
    if db_session:
        db_session.close()

    return thumbnails


def generate_thumbnail_filename(photo: PhotoInfo) -> str:
    """Generate a unique filename for the thumbnail."""
    # Create a hash based on photo UUID and original file path
    content = f"{photo.uuid}_{photo.original_path}"
    hash_obj = hashlib.md5(content.encode())
    hash_hex = hash_obj.hexdigest()[:8]

    # Use original extension for thumbnail
    original_ext = Path(photo.original_path).suffix.lower()
    if original_ext in [".jpeg", ".jpg"]:
        thumbnail_ext = ".jpg"
    elif original_ext == ".png":
        thumbnail_ext = ".png"
    else:
        thumbnail_ext = ".jpg"

    return f"{hash_hex}_{photo.filename}{thumbnail_ext}"


def create_thumbnail_info(photo: PhotoInfo, thumbnail_path: str, config: ThumbnailConfig) -> ThumbnailInfo:
    """Create ThumbnailInfo object from generated thumbnail."""
    try:
        stat = os.stat(thumbnail_path)
        size = stat.st_size

        # Get actual dimensions
        with Image.open(thumbnail_path) as img:
            width, height = img.size

        return ThumbnailInfo(photo_uuid=photo.uuid, thumbnail_path=thumbnail_path, size=size, quality=config.quality, width=width, height=height, created_at=datetime.fromtimestamp(stat.st_mtime))

    except Exception as e:
        logger.error(f"Error creating thumbnail info: {e}")
        raise


def get_thumbnail_stats(output_dir: str) -> Dict[str, Any]:
    """Get statistics about generated thumbnails in the output directory."""
    try:
        path = Path(output_dir)
        if not path.exists():
            return {"exists": False, "file_count": 0, "total_size": 0}

        thumbnail_files = list(path.glob("*.jpg")) + list(path.glob("*.png"))
        total_size = sum(f.stat().st_size for f in thumbnail_files)

        return {"exists": True, "file_count": len(thumbnail_files), "total_size": total_size, "directory": str(path.absolute())}

    except Exception as e:
        logger.error(f"Error getting thumbnail stats: {e}")
        return {"exists": False, "error": str(e)}


def cleanup_thumbnails(output_dir: str, max_age_days: int = 30) -> int:
    """
    Clean up old thumbnails.

    Args:
        output_dir: Directory containing thumbnails
        max_age_days: Maximum age in days before a thumbnail is considered old

    Returns:
        Number of thumbnails removed
    """
    try:
        path = Path(output_dir)
        if not path.exists():
            return 0

        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        removed_count = 0

        for thumbnail_file in path.glob("*.jpg"):
            try:
                mtime = datetime.fromtimestamp(thumbnail_file.stat().st_mtime)
                if mtime < cutoff_date:
                    thumbnail_file.unlink()
                    removed_count += 1
            except Exception as e:
                logger.warning(f"Failed to remove {thumbnail_file}: {e}")

        return removed_count

    except Exception as e:
        logger.error(f"Error during thumbnail cleanup: {e}")
        return 0


def get_photo_thumbnail_details(photo_uuid: str, library_path: str = None) -> str:
    """
    Get detailed thumbnail information for a specific photo.

    Args:
        photo_uuid: UUID of the photo
        library_path: Photos库路径，如果不提供则使用默认库

    Returns:
        JSON string containing thumbnail details
    """
    import json

    from .collector import get_global_photosdb

    try:
        # 使用全局PhotosDB实例
        if library_path:
            photosdb = get_global_photosdb(library_path)
        else:
            # 如果没有提供路径，使用默认库
            import osxphotos
            photosdb = osxphotos.PhotosDB()

        photo = None

        # 使用QueryOptions查询特定UUID的照片，避免加载所有照片
        # 由于osxphotos不支持直接按UUID查询，我们使用最近30天的照片进行搜索
        from datetime import datetime, timedelta, timezone

        thirty_days_ago = datetime.now(tz=timezone.utc) - timedelta(days=30)
        current_date = datetime.now(tz=timezone.utc)

        options = osxphotos.QueryOptions(added_after=thirty_days_ago, added_before=current_date)
        recent_photos = photosdb.query(options)

        for p in recent_photos:
            if str(p.uuid) == photo_uuid:
                photo = p
                break

        # 如果在最近30天没找到，尝试查询更长时间范围
        if not photo:
            one_year_ago = datetime.now(tz=timezone.utc) - timedelta(days=365)
            options = osxphotos.QueryOptions(added_after=one_year_ago, added_before=current_date)
            older_photos = photosdb.query(options)

            for p in older_photos:
                if str(p.uuid) == photo_uuid:
                    photo = p
                    break

        if not photo:
            return json.dumps({"photo_uuid": photo_uuid, "original_path": "", "original_resolution": "Unknown", "thumbnails": []})

        # Get original photo info
        original_path = photo.path or ""
        original_resolution = f"{photo.width}x{photo.height}" if photo.width and photo.height else "Unknown"

        thumbnails = []

        # Check for system thumbnails using path_derivatives
        if hasattr(photo, "path_derivatives") and photo.path_derivatives:
            for i, derivative_path in enumerate(photo.path_derivatives):
                if os.path.exists(derivative_path):
                    try:
                        # Get file info
                        stat_info = os.stat(derivative_path)
                        size_bytes = stat_info.st_size

                        # Get image dimensions
                        from PIL import Image

                        with Image.open(derivative_path) as img:
                            width, height = img.size
                            resolution = f"{width}x{height}"

                        thumbnails.append({"path": derivative_path, "resolution": resolution, "size_bytes": size_bytes, "exists": True, "file_url": f"file://{derivative_path}"})
                    except Exception as e:
                        logger.warning(f"Error processing derivative {derivative_path}: {e}")
                        thumbnails.append({"path": derivative_path, "resolution": "Unknown", "size_bytes": 0, "exists": True, "file_url": f"file://{derivative_path}"})

        # Check for generated thumbnails in output directory
        home_dir = Path.home()
        thumbnail_dir = home_dir / ".photo_thumbnails"

        if thumbnail_dir.exists():
            # Look for thumbnails with this photo's UUID in the filename
            for thumbnail_file in thumbnail_dir.glob(f"*{photo_uuid}*"):
                if thumbnail_file.is_file():
                    try:
                        stat_info = thumbnail_file.stat()
                        size_bytes = stat_info.st_size

                        from PIL import Image

                        with Image.open(thumbnail_file) as img:
                            width, height = img.size
                            resolution = f"{width}x{height}"

                        thumbnails.append({"path": str(thumbnail_file), "resolution": resolution, "size_bytes": size_bytes, "exists": True, "file_url": f"file://{thumbnail_file}"})
                    except Exception as e:
                        logger.warning(f"Error processing generated thumbnail {thumbnail_file}: {e}")

        result = {"photo_uuid": photo_uuid, "original_path": original_path, "original_resolution": original_resolution, "thumbnails": thumbnails}

        return json.dumps(result)

    except Exception as e:
        logger.error(f"Error getting thumbnail details for {photo_uuid}: {e}")
        return json.dumps({"photo_uuid": photo_uuid, "original_path": "", "original_resolution": "Error", "thumbnails": [], "error": str(e)})
