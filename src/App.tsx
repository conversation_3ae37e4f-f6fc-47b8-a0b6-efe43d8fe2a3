import { useState, useEffect } from 'react';
import { invoke, convertFileSrc } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
// import { readDir } from '@tauri-apps/plugin-fs';
import './App.css';

interface Photo {
    uuid: string;
    filename: string;
    original_path: string;
    date_taken: number;
    width: number;
    height: number;
    file_size: number;
    thumbnail_path?: string;
    // 算法计算结果
    phash?: string;
    ahash?: string;
    dhash?: string;
    whash?: string;
    similarity_scores?: { [key: string]: number }; // 与其他照片的相似度分数
}

interface SimilarityGroup {
    group_id: string;
    photos: Photo[];
    similarity_score: number;
    photo_count: number;
}

function App() {
    const [photos, setPhotos] = useState<Photo[]>([]);
    const [similarityGroups, setSimilarityGroups] = useState<SimilarityGroup[]>(
        []
    );
    const [selectedPath, setSelectedPath] = useState<string>('');
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState<'photos' | 'duplicates'>(
        'photos'
    );
    const [defaultLibraryChecked, setDefaultLibraryChecked] = useState(false);
    const [photosPerRow, setPhotosPerRow] = useState<number>(8); // 默认每行8张图片
    const [duplicatesPerRow, setDuplicatesPerRow] = useState<number>(6); // 相似照片默认每行6张
    const [daysBack, setDaysBack] = useState<number>(5); // 默认查看最近5天的照片

    // 算法参数状态
    const [algorithmConfig, setAlgorithmConfig] = useState({
        timeThresholdSeconds: 300, // 5分钟
        similarityThreshold: 0.85, // 85%
        showAlgorithmDetails: false // 是否显示算法详情面板
    });

    // 分批加载相关状态
    const [batchLoadingEnabled, setBatchLoadingEnabled] = useState(true); // 默认启用分批加载
    const [minPhotosRequired, setMinPhotosRequired] = useState(50); // 最少需要的照片数量
    const [currentDate, setCurrentDate] = useState<Date>(() => {
        // 确保不会搜索未来日期，最多到今天
        const today = new Date();
        today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
        return today;
    }); // 当前加载到的日期
    const [photosByDate, setPhotosByDate] = useState<Map<string, Photo[]>>(
        new Map()
    ); // 按日期分组的照片
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [hasMorePhotos, setHasMorePhotos] = useState(true);

    // 优化分析相关状态
    const [useOptimizedAnalysis, setUseOptimizedAnalysis] = useState(true); // 默认启用优化分析
    const [optimizedConfig, setOptimizedConfig] = useState({
        batchSize: 50,
        maxWorkers: 4,
        ahashThreshold: 0.9,
        phashThreshold: 0.85
    });

    // 控制每张照片的算法详情显示
    const [expandedPhotoDetails, setExpandedPhotoDetails] = useState<
        Set<string>
    >(new Set());









    const [progress, setProgress] = useState<{
        phase: string;
        current: number;
        total: number;
        message: string;
    } | null>(null);

    // 按日期分组照片
    const groupPhotosByDate = (photosToGroup: Photo[]) => {
        const grouped = new Map<string, Photo[]>();
        photosToGroup.forEach(photo => {
            const date = new Date(photo.date_taken * 1000);
            const dateKey = date.toDateString();
            if (!grouped.has(dateKey)) {
                grouped.set(dateKey, []);
            }
            grouped.get(dateKey)!.push(photo);
        });
        setPhotosByDate(grouped);
    };

    // 切换照片算法详情显示
    const togglePhotoDetails = (photoUuid: string) => {
        setExpandedPhotoDetails(prev => {
            const newSet = new Set(prev);
            if (newSet.has(photoUuid)) {
                newSet.delete(photoUuid);
            } else {
                newSet.add(photoUuid);
            }
            return newSet;
        });
    };

    // 在组件加载时自动检测默认 Photos Library
    useEffect(() => {
        // 防止React StrictMode导致的重复调用
        let isCancelled = false;

        const initializeLibrary = async () => {
            if (!isCancelled) {
                await checkDefaultPhotosLibrary();
            }
        };

        initializeLibrary();

        return () => {
            isCancelled = true;
        };
    }, []);

    // 监听时间范围变化，自动重新加载照片
    useEffect(() => {
        if (selectedPath && photos.length > 0) {
            console.log(`🔄 时间范围已更改为 ${daysBack} 天，重新加载照片...`);
            loadPhotos(selectedPath);
        }
    }, [daysBack]);

    // 响应式布局：根据屏幕宽度自动调整列数
    useEffect(() => {
        const handleResize = () => {
            const width = window.innerWidth;
            if (width <= 576) {
                setPhotosPerRow(Math.min(photosPerRow, 4)); // 小屏幕最多4列
                setDuplicatesPerRow(Math.min(duplicatesPerRow, 4));
            } else if (width <= 768) {
                setPhotosPerRow(Math.min(photosPerRow, 6)); // 中等屏幕最多6列
                setDuplicatesPerRow(Math.min(duplicatesPerRow, 5));
            }
            // 大屏幕保持用户设置的值
        };

        handleResize(); // 初始调用
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [photosPerRow, duplicatesPerRow]);

    // 无限滚动监听
    useEffect(() => {
        if (!batchLoadingEnabled) return;

        const handleScroll = () => {
            if (isLoadingMore || !hasMorePhotos) return;

            const scrollTop =
                window.pageYOffset || document.documentElement.scrollTop;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;

            // 当滚动到距离底部200px时，加载更多
            if (scrollTop + windowHeight >= documentHeight - 200) {
                console.log('📜 触发无限滚动');
                // 传统滚动：加载下一天
                console.log('加载下一天的照片');
                loadNextDay();
            }
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [
        batchLoadingEnabled,
        isLoadingMore,
        hasMorePhotos
    ]);



    async function checkDefaultPhotosLibrary(retryCount: number = 0) {
        if (defaultLibraryChecked) {
            console.log('🔄 默认库已检查过，跳过');
            return;
        }

        console.log(
            `🔍 开始检查默认 Photos Library (尝试 ${retryCount + 1}/6)`
        );

        try {
            setIsLoading(true);

            // 检查Tauri API是否已初始化
            if (typeof invoke === 'undefined') {
                throw new Error('Tauri API not initialized');
            }

            console.log('📞 调用 get_default_photos_library 命令...');
            const defaultLibrary = (await invoke(
                'get_default_photos_library'
            )) as string | null;

            console.log(
                '📞 get_default_photos_library 返回结果:',
                defaultLibrary
            );

            if (defaultLibrary) {
                console.log('✅ 找到默认 Photos Library:', defaultLibrary);
                setSelectedPath(defaultLibrary);
                console.log('🚀 开始加载照片...');
                await loadPhotos(defaultLibrary);
            } else {
                console.log('❌ 未找到默认 Photos Library，需要手动选择');
            }
        } catch (error) {
            console.error('❌ 检查默认 Photos Library 失败:', error);
            console.error('错误详情:', {
                message: String(error),
                type: typeof error,
                stack: error instanceof Error ? error.stack : 'No stack trace'
            });

            // 如果是Tauri API未初始化的错误，等待后重试（最多重试5次）
            if (
                retryCount < 5 &&
                (String(error).includes('invoke') ||
                    String(error).includes('undefined') ||
                    String(error).includes('not initialized'))
            ) {
                console.log(
                    `⏳ Tauri API 可能还未初始化，1秒后重试... (${retryCount + 1
                    }/5)`
                );
                setTimeout(() => {
                    setDefaultLibraryChecked(false);
                    checkDefaultPhotosLibrary(retryCount + 1);
                }, 1000);
                return;
            } else {
                console.log('🛑 达到最大重试次数或遇到其他错误，停止重试');
            }
        } finally {
            setIsLoading(false);
            setDefaultLibraryChecked(true);
            console.log('🏁 checkDefaultPhotosLibrary 完成');
        }
    }



    async function loadPhotos(path: string) {
        console.log('🚀 loadPhotos 开始，路径:', path);
        setIsLoading(true);
        setProgress(null);
        try {
            // 验证路径
            console.log('🔍 验证库路径...');
            const isValid = await invoke('validate_library_path', { path });
            console.log('📞 validate_library_path 返回:', isValid);

            if (!isValid) {
                console.error('❌ 路径验证失败');
                alert('无效的 Photos Library 路径');
                return;
            }

            // 再次确认是 Photos Library
            if (!path.endsWith('.photoslibrary')) {
                console.error('❌ 不是 Photos Library 文件');
                alert('只支持 Photos Library.photoslibrary 文件');
                return;
            }

            console.log('✅ 路径验证通过');

            if (batchLoadingEnabled) {
                // 分批加载模式
                console.log('📦 启用分批加载模式');
                setPhotos([]); // 清空现有照片
                setPhotosByDate(new Map()); // 清空按日期分组的照片
                setSimilarityGroups([]); // 清空相似组

                // 确保currentDate设置为今天的最后一刻，这样搜索会从今天开始
                const today = new Date();
                today.setHours(23, 59, 59, 999);
                setCurrentDate(today);
                console.log(`📅 设置搜索起始日期为今天: ${today.toDateString()}`);

                setHasMorePhotos(true);

                // 立即设置selectedPath，确保后续函数可以使用
                setSelectedPath(path);

                // 智能加载：确保至少获取指定数量的照片
                console.log(`📅 使用智能加载模式，目标至少${minPhotosRequired}张照片`);
                await loadPhotosUntilMinimum(minPhotosRequired, path);
            } else {
                // 传统模式：一次性加载所有照片（不限制数量）
                console.log('📦 使用传统加载模式');

                // 立即设置selectedPath，确保后续函数可以使用
                setSelectedPath(path);

                console.log('📞 调用 collect_photos_from_library，参数:', {
                    libraryPath: path,
                    daysBack: daysBack,
                    maxPhotos: 0
                });

                const loadedPhotos: Photo[] = await invoke(
                    'collect_photos_from_library',
                    {
                        libraryPath: path, // 使用驼峰命名
                        daysBack: daysBack,
                        maxPhotos: 0 // 0表示不限制数量
                    }
                );

                console.log(
                    '📸 collect_photos_from_library 返回照片数量:',
                    loadedPhotos.length
                );
                setPhotos(loadedPhotos);

                // 分析相似性
                console.log('🔍 开始分析照片相似性...');
                await analyzePhotos(loadedPhotos);
            }
        } catch (error) {
            console.error('❌ 加载照片失败:', error);
            console.error('错误详情:', {
                message: String(error),
                type: typeof error,
                stack: error instanceof Error ? error.stack : 'No stack trace'
            });
            alert('加载照片时出错: ' + error);
        } finally {
            setIsLoading(false);
            setProgress(null);
            console.log('🏁 loadPhotos 完成');
        }
    }

    // 智能加载：持续加载照片直到达到最小数量
    async function loadPhotosUntilMinimum(
        minPhotos: number,
        libraryPath: string
    ) {
        console.log(`🎯 开始智能加载，目标至少 ${minPhotos} 张照片`);

        let totalPhotos: Photo[] = [];
        let currentSearchDate = new Date();
        let daysSearched = 0;
        const maxDaysToSearch = 365; // 最多搜索一年
        let initialDaysBack = daysBack; // 初始搜索天数

        try {
            while (totalPhotos.length < minPhotos && daysSearched < maxDaysToSearch) {
                console.log(`🔍 第 ${Math.floor(daysSearched / initialDaysBack) + 1} 轮搜索，已获得 ${totalPhotos.length} 张照片`);

                // 每次搜索指定天数的范围
                const batchPhotos = await loadPhotosForDateRange(
                    currentSearchDate,
                    initialDaysBack,
                    undefined,
                    libraryPath
                );

                if (batchPhotos.length > 0) {
                    // 过滤重复照片（基于UUID）
                    const existingUuids = new Set(totalPhotos.map(p => p.uuid));
                    const newPhotos = batchPhotos.filter(p => !existingUuids.has(p.uuid));

                    totalPhotos.push(...newPhotos);
                    console.log(`📸 本轮新增 ${newPhotos.length} 张照片，总计 ${totalPhotos.length} 张`);

                    // 如果这是第一批照片，更新currentDate为搜索的起始日期（而不是照片的日期）
                    // 这样可以确保UI显示从今天开始，而不是跳到照片的实际日期
                    if (totalPhotos.length === newPhotos.length) {
                        // 第一批照片，保持currentDate为搜索起始日期
                        setCurrentDate(new Date(currentSearchDate));
                        console.log(`📅 设置显示起始日期为: ${currentSearchDate.toDateString()}`);
                    }
                } else {
                    console.log(`📭 ${currentSearchDate.toDateString()} 开始的 ${initialDaysBack} 天内没有照片`);
                }

                // 向前推进搜索日期
                currentSearchDate.setDate(currentSearchDate.getDate() - initialDaysBack);
                daysSearched += initialDaysBack;

                // 如果照片数量仍然不足，但已经搜索了很多天，可以考虑增加每次搜索的天数
                if (totalPhotos.length < minPhotos / 2 && daysSearched > 30) {
                    initialDaysBack = Math.min(initialDaysBack * 2, 30); // 最多30天一批
                    console.log(`📈 增加搜索范围到 ${initialDaysBack} 天/批`);
                }
            }

            if (totalPhotos.length >= minPhotos) {
                console.log(`✅ 智能加载完成！获得 ${totalPhotos.length} 张照片（目标 ${minPhotos} 张）`);
                console.log(`📊 搜索了 ${daysSearched} 天，平均每天 ${(totalPhotos.length / daysSearched).toFixed(2)} 张照片`);
            } else {
                console.log(`⚠️ 智能加载结束，仅获得 ${totalPhotos.length} 张照片（目标 ${minPhotos} 张）`);
                console.log(`📊 已搜索 ${daysSearched} 天，可能照片库中照片数量有限`);
                setHasMorePhotos(false);
            }

            // 设置最终的照片列表
            setPhotos(totalPhotos);

            // 按日期分组照片并更新currentDate为实际的最新照片日期
            if (totalPhotos.length > 0) {
                // 按日期分组
                groupPhotosByDate(totalPhotos);

                // 找到最新的照片日期，更新currentDate
                const latestPhoto = totalPhotos.reduce((latest, photo) =>
                    photo.date_taken > latest.date_taken ? photo : latest
                );
                const latestPhotoDate = new Date(latestPhoto.date_taken * 1000);
                setCurrentDate(latestPhotoDate);
                console.log(`📅 更新显示日期为最新照片日期: ${latestPhotoDate.toDateString()}`);

                console.log('🔍 开始分析照片相似性...');
                await analyzePhotos(totalPhotos);
            }

        } catch (error) {
            console.error('❌ 智能加载失败:', error);
            throw error;
        }
    }

    // 加载指定天数范围的照片
    async function loadPhotosForDateRange(
        startDate: Date,
        daysCount: number,
        maxPhotos?: number,
        libraryPath?: string
    ) {
        try {
            setIsLoadingMore(true);

            // 计算开始和结束时间戳
            const startOfRange = new Date(startDate);
            startOfRange.setHours(0, 0, 0, 0);

            const endOfRange = new Date(startDate);
            endOfRange.setDate(endOfRange.getDate() - daysCount + 1); // 向前推daysCount天，但包含起始日
            endOfRange.setHours(0, 0, 0, 0); // 设置为最早日期的开始

            const startTimestamp = Math.floor(endOfRange.getTime() / 1000); // 注意：这里是较早的日期
            const endTimestamp = Math.floor(startOfRange.getTime() / 1000); // 这里是较晚的日期

            console.log(`🔍 加载日期范围: ${daysCount}天`);
            console.log(
                `📅 从 ${endOfRange.toDateString()} 到 ${startOfRange.toDateString()}`
            );
            console.log(`📅 时间戳范围: ${startTimestamp} - ${endTimestamp}`);
            console.log(
                `📅 日期范围: ${endOfRange.toISOString()} - ${startOfRange.toISOString()}`
            );

            const pathToUse = libraryPath || selectedPath;
            console.log(`📂 传入的libraryPath: ${libraryPath}`);
            console.log(`📂 当前selectedPath: ${selectedPath}`);
            console.log(`📂 最终使用的路径: ${pathToUse}`);
            console.log(`🔢 最大照片数: ${maxPhotos || '无限制'}`);

            if (!pathToUse) {
                console.error('❌ 照片库路径为空，无法加载照片');
                return [];
            }

            // 调用API获取指定日期范围的照片
            console.log('🚀 开始调用 collect_photos_by_date_range...');
            console.log('📋 调用参数:', {
                libraryPath: pathToUse,
                startDate: startTimestamp,
                endDate: endTimestamp,
                maxPhotos: maxPhotos || null
            });

            const rangePhotos: Photo[] = await invoke(
                'collect_photos_by_date_range',
                {
                    libraryPath: pathToUse,
                    startDate: startTimestamp,
                    endDate: endTimestamp,
                    maxPhotos: maxPhotos || null
                }
            );

            console.log(
                `✅ collect_photos_by_date_range 调用完成，返回 ${rangePhotos.length} 张照片`
            );

            if (rangePhotos.length === 0) {
                console.log(`📭 ${daysCount}天范围内没有找到照片`);
                return [];
            }

            console.log(
                `${daysCount}天范围内找到 ${rangePhotos.length} 张照片`
            );

            // 调试：检查缩略图路径
            const withThumbnails = rangePhotos.filter(p => p.thumbnail_path);
            const withoutThumbnails = rangePhotos.filter(
                p => !p.thumbnail_path
            );
            console.log(
                `📸 ${daysCount}天范围 - 有缩略图: ${withThumbnails.length}, 无缩略图: ${withoutThumbnails.length}`
            );

            if (withThumbnails.length > 0) {
                console.log(
                    `📁 缩略图路径示例: ${withThumbnails[0].thumbnail_path}`
                );
            }
            if (withoutThumbnails.length > 0) {
                console.log(
                    `❌ 无缩略图的照片示例: ${withoutThumbnails[0].filename}`
                );
            }

            return rangePhotos;
        } catch (error) {
            console.error(`❌ 加载 ${daysCount}天范围的照片失败:`, error);
            console.error(`❌ 错误详情:`, {
                daysCount,
                startDate: startDate.toDateString(),
                selectedPath,
                error: error
            });
            return [];
        } finally {
            setIsLoadingMore(false);
        }
    }

    // 分批加载一天的照片
    async function loadPhotosForDate(
        date: Date,
        maxPhotos?: number,
        libraryPath?: string
    ) {
        try {
            setIsLoadingMore(true);

            // 计算当天的开始和结束时间戳
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            const startTimestamp = Math.floor(startOfDay.getTime() / 1000);
            const endTimestamp = Math.floor(endOfDay.getTime() / 1000);

            console.log(`🔍 加载日期 ${date.toDateString()} 的照片`);
            console.log(`📅 时间戳范围: ${startTimestamp} - ${endTimestamp}`);
            console.log(
                `📅 日期范围: ${startOfDay.toISOString()} - ${endOfDay.toISOString()}`
            );
            const pathToUse = libraryPath || selectedPath;
            console.log(`📂 传入的libraryPath: ${libraryPath}`);
            console.log(`📂 当前selectedPath: ${selectedPath}`);
            console.log(`📂 最终使用的路径: ${pathToUse}`);
            console.log(`🔢 最大照片数: ${maxPhotos || '无限制'}`);

            if (!pathToUse) {
                console.error('❌ 照片库路径为空，无法加载照片');
                return [];
            }

            // 调用新的API获取指定日期范围的照片（不限制数量）
            console.log('🚀 开始调用 collect_photos_by_date_range...');
            console.log('📋 调用参数:', {
                libraryPath: pathToUse,
                startDate: startTimestamp,
                endDate: endTimestamp,
                maxPhotos: maxPhotos || null
            });

            const dayPhotos: Photo[] = await invoke(
                'collect_photos_by_date_range',
                {
                    libraryPath: pathToUse, // 使用驼峰命名
                    startDate: startTimestamp,
                    endDate: endTimestamp,
                    maxPhotos: maxPhotos || null // 不传递maxPhotos或传递null表示不限制
                }
            );
            console.log(
                `✅ collect_photos_by_date_range 调用完成，返回 ${dayPhotos.length} 张照片`
            );

            if (dayPhotos.length === 0) {
                console.log(`📭 ${date.toDateString()} 没有找到照片`);
                return [];
            }

            console.log(
                `${date.toDateString()} 找到 ${dayPhotos.length} 张照片`
            );

            // 调试：检查缩略图路径
            const withThumbnails = dayPhotos.filter(p => p.thumbnail_path);
            const withoutThumbnails = dayPhotos.filter(p => !p.thumbnail_path);
            console.log(
                `📸 ${date.toDateString()} - 有缩略图: ${withThumbnails.length
                }, 无缩略图: ${withoutThumbnails.length}`
            );

            if (withThumbnails.length > 0) {
                console.log(
                    `📁 缩略图路径示例: ${withThumbnails[0].thumbnail_path}`
                );
            }
            if (withoutThumbnails.length > 0) {
                console.log(
                    `❌ 无缩略图的照片示例: ${withoutThumbnails[0].filename}`
                );
            }

            // 分析所有照片（包括新加载的）
            if (dayPhotos.length > 0) {
                // 获取当前所有照片（包括新加载的）
                const allCurrentPhotos = [...photos, ...dayPhotos];
                await analyzePhotos(allCurrentPhotos);
            }

            // 更新按日期分组的照片
            const dateKey = date.toDateString();
            setPhotosByDate(prev => {
                const newMap = new Map(prev);
                newMap.set(dateKey, dayPhotos);
                return newMap;
            });

            // 更新总的照片列表
            setPhotos(prev => [...prev, ...dayPhotos]);

            return dayPhotos;
        } catch (error) {
            console.error(`❌ 加载 ${date.toDateString()} 的照片失败:`, error);
            console.error(`❌ 错误详情:`, {
                date: date.toDateString(),
                selectedPath,
                startTimestamp: Math.floor(
                    new Date(date).setHours(0, 0, 0, 0) / 1000
                ),
                endTimestamp: Math.floor(
                    new Date(date).setHours(23, 59, 59, 999) / 1000
                ),
                error: error
            });
            return [];
        } finally {
            setIsLoadingMore(false);
        }
    }

    // 加载下一天的照片
    async function loadNextDay() {
        if (isLoadingMore || !hasMorePhotos) {
            return;
        }

        // 传统的单日加载
        const nextDate = new Date(currentDate);
        nextDate.setDate(nextDate.getDate() - 1);

        console.log(`准备加载下一天: ${nextDate.toDateString()}`);

        const dayPhotos = await loadPhotosForDate(
            nextDate,
            undefined,
            selectedPath
        );

        if (dayPhotos.length === 0) {
            // 如果连续几天都没有照片，可能已经到达最早的照片
            setHasMorePhotos(false);
        } else {
            setCurrentDate(nextDate);
        }
    }





    async function analyzePhotos(photosToAnalyze?: Photo[]) {
        const targetPhotos = photosToAnalyze || photos;
        if (targetPhotos.length === 0) {
            return;
        }

        try {
            setIsLoading(true);
            setProgress({
                phase: 'analyzing',
                current: 0,
                total: targetPhotos.length,
                message: '正在分析照片相似性...'
            });

            console.log('开始分析照片，使用配置:', algorithmConfig);
            console.log('优化分析配置:', optimizedConfig);

            let analysis: any;

            // 优先使用时间线分析器
            if (true) { // 默认启用时间线分析
                console.log('⏰ 使用时间线分析算法');
                analysis = await invoke('analyze_photos_by_timeline', {
                    photos: targetPhotos,
                    timeThresholdSeconds: algorithmConfig.timeThresholdSeconds || 300,
                    maxWorkers: optimizedConfig.maxWorkers || 4
                });

                // 显示时间线分析统计
                if (analysis.performance_stats) {
                    const stats = analysis.performance_stats;
                    console.log('📊 时间线分析统计:', stats);
                    setProgress({
                        phase: 'analyzing',
                        current: targetPhotos.length,
                        total: targetPhotos.length,
                        message: `时间线分析完成！分组数: ${stats.timeline_groups}, 平均每组: ${stats.avg_photos_per_group?.toFixed(1)} 张照片`
                    });
                }
            } else { }

            setSimilarityGroups(analysis.similarity_groups || []);
        } catch (error) {
            console.error('分析照片失败:', error);
            alert('分析照片失败: ' + error);
        } finally {
            setIsLoading(false);
            setProgress(null);
        }
    }







    return (
        <div className='app'>
            <header className='app-header'>


                <div className='path-selection'>
                    {!selectedPath && !isLoading && defaultLibraryChecked && (
                        <div className='header-actions'>
                            <p className='no-default-library'>
                                未找到默认 Photos Library
                            </p>
                        </div>
                    )}



                    {isLoading && (
                        <div className='loading-status'>
                            <p>正在加载 Photos Library...</p>
                            {progress && (
                                <div className='progress-info'>
                                    <p>{progress.message}</p>
                                    <div className='progress-bar-container'>
                                        <div
                                            className='progress-bar'
                                            style={{
                                                width: `${(progress.current /
                                                    progress.total) *
                                                    100
                                                    }%`
                                            }}
                                        ></div>
                                    </div>
                                    <p>
                                        {progress.current} / {progress.total}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </header>

            <div className='tab-navigation'>
                <button
                    className={activeTab === 'photos' ? 'active' : ''}
                    onClick={() => setActiveTab('photos')}
                >
                    所有照片 ({photos.length})
                </button>
                <button
                    className={activeTab === 'duplicates' ? 'active' : ''}
                    onClick={() => setActiveTab('duplicates')}
                >
                    相似照片 ({similarityGroups.length})
                </button>
                <button
                    className={
                        algorithmConfig.showAlgorithmDetails ? 'active' : ''
                    }
                    onClick={() =>
                        setAlgorithmConfig(prev => ({
                            ...prev,
                            showAlgorithmDetails: !prev.showAlgorithmDetails
                        }))
                    }
                >
                    算法参数
                </button>
            </div>

            {/* 算法参数控制面板 */}
            {algorithmConfig.showAlgorithmDetails && (
                <div className='algorithm-config-panel'>
                    <h3>算法参数配置</h3>

                    <div className='config-section'>
                        <h4>时间线参数</h4>
                        <div className='config-item'>
                            <label>
                                查看时间范围:
                                <input
                                    type='range'
                                    min='1'
                                    max='30'
                                    value={daysBack}
                                    onChange={e =>
                                        setDaysBack(Number(e.target.value))
                                    }
                                    className='time-range-slider'
                                />
                                <span className='config-value'>
                                    {daysBack} 天
                                </span>
                                <span className='config-help'>
                                    (查看最近 {daysBack} 天的照片)
                                </span>
                            </label>
                        </div>
                        <div className='config-item'>
                            <label>
                                时间阈值 (秒):
                                <input
                                    type='number'
                                    min='30'
                                    max='3600'
                                    value={algorithmConfig.timeThresholdSeconds}
                                    onChange={e =>
                                        setAlgorithmConfig(prev => ({
                                            ...prev,
                                            timeThresholdSeconds: Number(
                                                e.target.value
                                            )
                                        }))
                                    }
                                />
                                <span className='config-help'>
                                    (
                                    {Math.floor(
                                        algorithmConfig.timeThresholdSeconds /
                                        60
                                    )}
                                    分
                                    {algorithmConfig.timeThresholdSeconds % 60}
                                    秒)
                                </span>
                            </label>
                        </div>

                        <div className='config-item'>
                            <label>
                                相似度阈值:
                                <input
                                    type='range'
                                    min='0.5'
                                    max='1.0'
                                    step='0.05'
                                    value={algorithmConfig.similarityThreshold}
                                    onChange={e =>
                                        setAlgorithmConfig(prev => ({
                                            ...prev,
                                            similarityThreshold: Number(
                                                e.target.value
                                            )
                                        }))
                                    }
                                />
                                <span className='config-value'>
                                    {(
                                        algorithmConfig.similarityThreshold *
                                        100
                                    ).toFixed(0)}
                                    %
                                </span>
                            </label>
                        </div>
                    </div>

                    <div className='config-section'>
                        <h4>加载模式</h4>
                        <div className='config-item'>
                            <label className='checkbox-label'>
                                <input
                                    type='checkbox'
                                    checked={batchLoadingEnabled}
                                    onChange={e =>
                                        setBatchLoadingEnabled(e.target.checked)
                                    }
                                />
                                启用分批加载模式 (推荐)
                                <span className='algorithm-description'>
                                    (按天分批加载，支持无限滚动，智能获取足够照片)
                                </span>
                            </label>
                        </div>

                        {batchLoadingEnabled && (
                            <div className='config-item'>
                                <label>
                                    最少照片数量:
                                    <input
                                        type='number'
                                        min='20'
                                        max='200'
                                        value={minPhotosRequired}
                                        onChange={e =>
                                            setMinPhotosRequired(Number(e.target.value))
                                        }
                                    />
                                    <span className='config-description'>
                                        张
                                    </span>
                                    <span className='config-help'>
                                        (自动加载直到达到此数量)
                                    </span>
                                </label>
                            </div>
                        )}
                    </div>



                    <div className='config-section'>
                        <h4>🚀 优化分析算法</h4>
                        <div className='config-item'>
                            <label className='checkbox-label'>
                                <input
                                    type='checkbox'
                                    checked={useOptimizedAnalysis}
                                    onChange={e =>
                                        setUseOptimizedAnalysis(
                                            e.target.checked
                                        )
                                    }
                                />
                                启用优化分析 (推荐)
                                <span className='algorithm-description'>
                                    (分层检测: AHash快速筛选 + PHash精确验证)
                                </span>
                            </label>
                        </div>

                        {useOptimizedAnalysis && (
                            <div className='optimization-config'>
                                <div className='config-item'>
                                    <label>
                                        批处理大小:
                                        <input
                                            type='number'
                                            min='10'
                                            max='200'
                                            value={optimizedConfig.batchSize}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    batchSize:
                                                        parseInt(
                                                            e.target.value
                                                        ) || 50
                                                }))
                                            }
                                        />
                                        <span className='config-help'>
                                            (每批 {optimizedConfig.batchSize}{' '}
                                            张)
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        并行线程:
                                        <input
                                            type='number'
                                            min='1'
                                            max='16'
                                            value={optimizedConfig.maxWorkers}
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    maxWorkers:
                                                        parseInt(
                                                            e.target.value
                                                        ) || 4
                                                }))
                                            }
                                        />
                                        <span className='config-help'>
                                            ({optimizedConfig.maxWorkers} 线程)
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        AHash筛选阈值:
                                        <input
                                            type='range'
                                            min='0.8'
                                            max='0.98'
                                            step='0.02'
                                            value={
                                                optimizedConfig.ahashThreshold
                                            }
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    ahashThreshold: parseFloat(
                                                        e.target.value
                                                    )
                                                }))
                                            }
                                        />
                                        <span className='config-value'>
                                            {(
                                                optimizedConfig.ahashThreshold *
                                                100
                                            ).toFixed(0)}
                                            %
                                        </span>
                                    </label>
                                </div>

                                <div className='config-item'>
                                    <label>
                                        PHash验证阈值:
                                        <input
                                            type='range'
                                            min='0.75'
                                            max='0.95'
                                            step='0.02'
                                            value={
                                                optimizedConfig.phashThreshold
                                            }
                                            onChange={e =>
                                                setOptimizedConfig(prev => ({
                                                    ...prev,
                                                    phashThreshold: parseFloat(
                                                        e.target.value
                                                    )
                                                }))
                                            }
                                        />
                                        <span className='config-value'>
                                            {(
                                                optimizedConfig.phashThreshold *
                                                100
                                            ).toFixed(0)}
                                            %
                                        </span>
                                    </label>
                                </div>
                            </div>
                        )}
                    </div>



                    <div className='config-actions'>
                        <button
                            onClick={() => {
                                // 重新分析照片
                                if (photos.length > 0) {
                                    analyzePhotos();
                                }
                            }}
                            disabled={isLoading}
                            className='apply-config-btn'
                        >
                            应用配置并重新分析
                        </button>

                        <button
                            onClick={() => {
                                setDaysBack(5); // 重置时间范围为5天
                                setMinPhotosRequired(50); // 重置最小照片数量
                                setAlgorithmConfig({
                                    timeThresholdSeconds: 300,
                                    similarityThreshold: 0.85,
                                    showAlgorithmDetails: true
                                });
                                setOptimizedConfig({
                                    batchSize: 50,
                                    maxWorkers: 4,
                                    ahashThreshold: 0.9,
                                    phashThreshold: 0.85
                                });
                            }}
                            className='reset-config-btn'
                        >
                            重置为默认值
                        </button>

                        <button
                            onClick={async () => {
                                if (window.confirm('确定要清空数据库缓存吗？这将删除所有已缓存的照片数据和分析结果。')) {
                                    try {
                                        setIsLoading(true);
                                        const result = await invoke<string>('clear_database_cache');
                                        alert(result);
                                        // 清空当前显示的数据
                                        setPhotos([]);
                                        setSimilarityGroups([]);
                                    } catch (error) {
                                        console.error('清空数据库缓存失败:', error);
                                        alert(`清空数据库缓存失败: ${error}`);
                                    } finally {
                                        setIsLoading(false);
                                    }
                                }
                            }}
                            disabled={isLoading}
                            className='clear-cache-btn'
                        >
                            清空数据库缓存
                        </button>
                    </div>
                </div>
            )}

            <main className='app-main'>
                {isLoading && (
                    <div className='loading'>
                        <div className='spinner'></div>
                        <p>正在处理照片...</p>
                        {progress && (
                            <div className='progress-info'>
                                <p>{progress.message}</p>
                                <div className='progress-bar-container'>
                                    <div
                                        className='progress-bar'
                                        style={{
                                            width: `${(progress.current /
                                                progress.total) *
                                                100
                                                }%`
                                        }}
                                    ></div>
                                </div>
                                <p>
                                    {progress.current} / {progress.total}
                                </p>
                            </div>
                        )}
                    </div>
                )}



                {activeTab === 'photos' && photos.length > 0 && (
                    <div className='photo-controls'>
                        <label className='photos-per-row-control'>
                            每行显示图片数量:
                            <input
                                type='range'
                                min='6'
                                max='10'
                                value={photosPerRow}
                                onChange={e =>
                                    setPhotosPerRow(Number(e.target.value))
                                }
                                className='photos-per-row-slider'
                            />
                            <span className='photos-per-row-value'>
                                {photosPerRow}
                            </span>
                        </label>


                    </div>
                )}

                {activeTab === 'photos' && (
                    <div className='photos-container'>
                        {batchLoadingEnabled ? (
                            // 分批加载模式：按日期分组显示
                            <div className='photos-by-date'>
                                {Array.from(photosByDate.entries())
                                    .sort(
                                        ([dateA], [dateB]) =>
                                            new Date(dateB).getTime() -
                                            new Date(dateA).getTime()
                                    )
                                    .map(([dateKey, dayPhotos]) => (
                                        <div
                                            key={dateKey}
                                            className='date-group'
                                        >
                                            <h3 className='date-header'>
                                                {new Date(
                                                    dateKey
                                                ).toLocaleDateString('zh-CN', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    weekday: 'long'
                                                })}{' '}
                                                ({dayPhotos.length} 张照片)
                                            </h3>
                                            <div
                                                className='photos-grid'
                                                style={{
                                                    gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`
                                                }}
                                            >
                                                {dayPhotos.map(photo => (
                                                    <div
                                                        key={photo.uuid}
                                                        className='photo-card'
                                                    >
                                                        {photo.thumbnail_path ? (
                                                            <img
                                                                src={convertFileSrc(
                                                                    photo.thumbnail_path
                                                                )}
                                                                alt={
                                                                    photo.filename
                                                                }
                                                                className='photo-thumbnail'
                                                                onLoad={() => {
                                                                    console.log(
                                                                        `✅ 缩略图加载成功: ${photo.thumbnail_path}`
                                                                    );
                                                                }}
                                                                onError={e => {
                                                                    console.error(
                                                                        `❌ 缩略图加载失败: ${photo.thumbnail_path}`
                                                                    );
                                                                    console.error(
                                                                        `转换后的URL: ${convertFileSrc(
                                                                            photo.thumbnail_path ||
                                                                            ''
                                                                        )}`
                                                                    );
                                                                    e.currentTarget.style.display =
                                                                        'none';
                                                                }}
                                                            />
                                                        ) : (
                                                            <div className='photo-placeholder'>
                                                                <span>
                                                                    无缩略图
                                                                </span>
                                                                <small
                                                                    style={{
                                                                        display:
                                                                            'block',
                                                                        fontSize:
                                                                            '10px',
                                                                        color: '#666'
                                                                    }}
                                                                >
                                                                    {
                                                                        photo.filename
                                                                    }
                                                                </small>
                                                            </div>
                                                        )}
                                                        <div className='photo-info'>
                                                            <h3>
                                                                {photo.filename}
                                                            </h3>
                                                            <p>
                                                                尺寸:{' '}
                                                                {photo.width} ×{' '}
                                                                {photo.height}
                                                            </p>
                                                            <p>
                                                                大小:{' '}
                                                                {(
                                                                    photo.file_size /
                                                                    1024 /
                                                                    1024
                                                                ).toFixed(
                                                                    2
                                                                )}{' '}
                                                                MB
                                                            </p>
                                                            <p>
                                                                拍摄时间:{' '}
                                                                {new Date(
                                                                    photo.date_taken *
                                                                    1000
                                                                ).toLocaleString()}
                                                            </p>

                                                            {/* 算法详情切换按钮 */}
                                                            <button
                                                                className='toggle-details-btn'
                                                                onClick={() =>
                                                                    togglePhotoDetails(
                                                                        photo.uuid
                                                                    )
                                                                }
                                                            >
                                                                {expandedPhotoDetails.has(
                                                                    photo.uuid
                                                                )
                                                                    ? '隐藏'
                                                                    : '显示'}
                                                                算法详情
                                                            </button>

                                                            {/* 算法详情展开区域 */}
                                                            {expandedPhotoDetails.has(
                                                                photo.uuid
                                                            ) && (
                                                                    <div className='photo-algorithm-details'>
                                                                        <h4>
                                                                            Hash值
                                                                        </h4>
                                                                        {photo.phash && (
                                                                            <p>
                                                                                <strong>
                                                                                    PHash:
                                                                                </strong>{' '}
                                                                                {photo.phash.substring(
                                                                                    0,
                                                                                    16
                                                                                )}
                                                                                ...
                                                                            </p>
                                                                        )}
                                                                        {photo.ahash && (
                                                                            <p>
                                                                                <strong>
                                                                                    AHash:
                                                                                </strong>{' '}
                                                                                {photo.ahash.substring(
                                                                                    0,
                                                                                    16
                                                                                )}
                                                                                ...
                                                                            </p>
                                                                        )}
                                                                        {photo.dhash && (
                                                                            <p>
                                                                                <strong>
                                                                                    DHash:
                                                                                </strong>{' '}
                                                                                {photo.dhash.substring(
                                                                                    0,
                                                                                    16
                                                                                )}
                                                                                ...
                                                                            </p>
                                                                        )}
                                                                        {photo.whash && (
                                                                            <p>
                                                                                <strong>
                                                                                    WHash:
                                                                                </strong>{' '}
                                                                                {photo.whash.substring(
                                                                                    0,
                                                                                    16
                                                                                )}
                                                                                ...
                                                                            </p>
                                                                        )}

                                                                        {photo.similarity_scores &&
                                                                            Object.keys(
                                                                                photo.similarity_scores
                                                                            )
                                                                                .length >
                                                                            0 && (
                                                                                <div className='similarity-scores'>
                                                                                    <h4>
                                                                                        相似度分数
                                                                                    </h4>
                                                                                    {Object.entries(
                                                                                        photo.similarity_scores
                                                                                    )
                                                                                        .slice(
                                                                                            0,
                                                                                            3
                                                                                        )
                                                                                        .map(
                                                                                            ([
                                                                                                uuid,
                                                                                                score
                                                                                            ]) => (
                                                                                                <p
                                                                                                    key={
                                                                                                        uuid
                                                                                                    }
                                                                                                >
                                                                                                    <strong>
                                                                                                        与{' '}
                                                                                                        {uuid.substring(
                                                                                                            0,
                                                                                                            8
                                                                                                        )}
                                                                                                        ...:
                                                                                                    </strong>{' '}
                                                                                                    {(
                                                                                                        score *
                                                                                                        100
                                                                                                    ).toFixed(
                                                                                                        1
                                                                                                    )}

                                                                                                    %
                                                                                                </p>
                                                                                            )
                                                                                        )}
                                                                                </div>
                                                                            )}
                                                                    </div>
                                                                )}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    ))}

                                {/* 加载更多指示器 */}
                                {isLoadingMore && (
                                    <div className='loading-more'>
                                        <div className='spinner'></div>
                                        <p>正在加载更多照片...</p>
                                    </div>
                                )}



                                {!hasMorePhotos && photos.length > 0 && (
                                    <div className='no-more-photos'>
                                        <p>已加载所有照片</p>
                                    </div>
                                )}
                            </div>
                        ) : (
                            // 传统模式：统一网格显示
                            <div
                                className='photos-grid'
                                style={{
                                    gridTemplateColumns: `repeat(${photosPerRow}, 1fr)`
                                }}
                            >
                                {photos.map(photo => (
                                    <div
                                        key={photo.uuid}
                                        className='photo-card'
                                    >
                                        {photo.thumbnail_path ? (
                                            <img
                                                src={convertFileSrc(
                                                    photo.thumbnail_path
                                                )}
                                                alt={photo.filename}
                                                className='photo-thumbnail'
                                                onLoad={() => {
                                                    console.log(
                                                        `✅ 缩略图加载成功: ${photo.thumbnail_path}`
                                                    );
                                                }}
                                                onError={e => {
                                                    console.error(
                                                        `❌ 缩略图加载失败: ${photo.thumbnail_path}`
                                                    );
                                                    console.error(
                                                        `转换后的URL: ${convertFileSrc(
                                                            photo.thumbnail_path ||
                                                            ''
                                                        )}`
                                                    );
                                                    e.currentTarget.style.display =
                                                        'none';
                                                }}
                                            />
                                        ) : (
                                            <div className='photo-placeholder'>
                                                <span>无缩略图</span>
                                                <small
                                                    style={{
                                                        display: 'block',
                                                        fontSize: '10px',
                                                        color: '#666'
                                                    }}
                                                >
                                                    {photo.filename}
                                                </small>
                                            </div>
                                        )}
                                        <div className='photo-info'>
                                            <h3>{photo.filename}</h3>
                                            <p>
                                                尺寸: {photo.width} ×{' '}
                                                {photo.height}
                                            </p>
                                            <p>
                                                大小:{' '}
                                                {(
                                                    photo.file_size /
                                                    1024 /
                                                    1024
                                                ).toFixed(2)}{' '}
                                                MB
                                            </p>
                                            <p>
                                                拍摄时间:{' '}
                                                {new Date(
                                                    photo.date_taken * 1000
                                                ).toLocaleString()}
                                            </p>

                                            {/* 算法详情切换按钮 */}
                                            <button
                                                className='toggle-details-btn'
                                                onClick={() =>
                                                    togglePhotoDetails(
                                                        photo.uuid
                                                    )
                                                }
                                            >
                                                {expandedPhotoDetails.has(
                                                    photo.uuid
                                                )
                                                    ? '隐藏'
                                                    : '显示'}
                                                算法详情
                                            </button>

                                            {/* 算法详情展开区域 */}
                                            {expandedPhotoDetails.has(
                                                photo.uuid
                                            ) && (
                                                    <div className='photo-algorithm-details'>
                                                        <h4>Hash值</h4>
                                                        {photo.phash && (
                                                            <p>
                                                                <strong>
                                                                    PHash:
                                                                </strong>{' '}
                                                                {photo.phash.substring(
                                                                    0,
                                                                    16
                                                                )}
                                                                ...
                                                            </p>
                                                        )}
                                                        {photo.ahash && (
                                                            <p>
                                                                <strong>
                                                                    AHash:
                                                                </strong>{' '}
                                                                {photo.ahash.substring(
                                                                    0,
                                                                    16
                                                                )}
                                                                ...
                                                            </p>
                                                        )}
                                                        {photo.dhash && (
                                                            <p>
                                                                <strong>
                                                                    DHash:
                                                                </strong>{' '}
                                                                {photo.dhash.substring(
                                                                    0,
                                                                    16
                                                                )}
                                                                ...
                                                            </p>
                                                        )}
                                                        {photo.whash && (
                                                            <p>
                                                                <strong>
                                                                    WHash:
                                                                </strong>{' '}
                                                                {photo.whash.substring(
                                                                    0,
                                                                    16
                                                                )}
                                                                ...
                                                            </p>
                                                        )}

                                                        {photo.similarity_scores &&
                                                            Object.keys(
                                                                photo.similarity_scores
                                                            ).length > 0 && (
                                                                <div className='similarity-scores'>
                                                                    <h4>
                                                                        相似度分数
                                                                    </h4>
                                                                    {Object.entries(
                                                                        photo.similarity_scores
                                                                    )
                                                                        .slice(0, 3)
                                                                        .map(
                                                                            ([
                                                                                uuid,
                                                                                score
                                                                            ]) => (
                                                                                <p
                                                                                    key={
                                                                                        uuid
                                                                                    }
                                                                                >
                                                                                    <strong>
                                                                                        与{' '}
                                                                                        {uuid.substring(
                                                                                            0,
                                                                                            8
                                                                                        )}
                                                                                        ...:
                                                                                    </strong>{' '}
                                                                                    {(
                                                                                        score *
                                                                                        100
                                                                                    ).toFixed(
                                                                                        1
                                                                                    )}

                                                                                    %
                                                                                </p>
                                                                            )
                                                                        )}
                                                                </div>
                                                            )}
                                                    </div>
                                                )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                )}

                {activeTab === 'duplicates' && similarityGroups.length > 0 && (
                    <div className='photo-controls'>
                        <label className='photos-per-row-control'>
                            每行显示图片数量:
                            <input
                                type='range'
                                min='4'
                                max='8'
                                value={duplicatesPerRow}
                                onChange={e =>
                                    setDuplicatesPerRow(Number(e.target.value))
                                }
                                className='photos-per-row-slider'
                            />
                            <span className='photos-per-row-value'>
                                {duplicatesPerRow}
                            </span>
                        </label>


                    </div>
                )}

                {activeTab === 'duplicates' && (
                    <div className='duplicate-groups'>
                        {/* 显示统计信息 */}
                        {similarityGroups.length > 0 && (
                            <div className='similarity-stats'>
                                <p>
                                    总共找到 {similarityGroups.length} 个相似组
                                </p>
                            </div>
                        )}

                        {/* 显示相似组 */}
                        {similarityGroups.map(group => (
                            <div
                                key={group.group_id}
                                className='duplicate-group'
                            >
                                <div className='group-header'>
                                    <h3>相似组 #{group.group_id}</h3>
                                    <span>包含 {group.photo_count} 张照片</span>
                                    <span>
                                        相似度:{' '}
                                        {(group.similarity_score * 100).toFixed(
                                            1
                                        )}
                                        %
                                    </span>
                                </div>
                                <div
                                    className='group-photos'
                                    style={{
                                        gridTemplateColumns: `repeat(${duplicatesPerRow}, 1fr)`
                                    }}
                                >
                                    {group.photos.map(photo => (
                                        <div
                                            key={photo.uuid}
                                            className='group-photo'
                                        >
                                            {photo.thumbnail_path ? (
                                                <img
                                                    src={convertFileSrc(
                                                        photo.thumbnail_path
                                                    )}
                                                    alt={photo.filename}
                                                    className='group-thumbnail'
                                                    onError={e => {
                                                        console.error(
                                                            `Failed to load group thumbnail: ${photo.thumbnail_path}`
                                                        );
                                                        e.currentTarget.style.display =
                                                            'none';
                                                    }}
                                                />
                                            ) : (
                                                <div className='group-placeholder'>
                                                    <span>无缩略图</span>
                                                </div>
                                            )}
                                            <div className='group-photo-info'>
                                                <h4>{photo.filename}</h4>
                                                <p>
                                                    尺寸: {photo.width} ×{' '}
                                                    {photo.height}
                                                </p>
                                                <p>
                                                    大小:{' '}
                                                    {(
                                                        photo.file_size /
                                                        1024 /
                                                        1024
                                                    ).toFixed(2)}{' '}
                                                    MB
                                                </p>
                                                <p>
                                                    拍摄时间:{' '}
                                                    {new Date(
                                                        photo.date_taken * 1000
                                                    ).toLocaleString()}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))}


                    </div>
                )}

                {!selectedPath && !isLoading && (
                    <div className='welcome'>
                        <h2>欢迎使用照片缩略图管理器</h2>
                        <p>点击"选择照片库/目录"开始浏览和管理您的照片</p>
                    </div>
                )}
            </main>
        </div>
    );
}

export default App;
